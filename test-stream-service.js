const streamService = require('./services/Stream.service');

async function testStreamService() {
    try {
        console.log('Testing streamService.fetchAll()...');
        const result = await streamService.fetchAll({});
        
        console.log('Result type:', typeof result);
        console.log('Is array:', Array.isArray(result));
        console.log('Length:', result?.length || 'N/A');
        
        if (Array.isArray(result)) {
            console.log('✅ streamService.fetchAll() returned an array');
            console.log('Sample data:', result.slice(0, 2));
        } else {
            console.log('❌ streamService.fetchAll() did not return an array:', result);
        }
    } catch (error) {
        console.error('❌ Error testing streamService.fetchAll():', error.message);
    }
}

testStreamService();
